use std::ops::Mul;

use candle_core::Tensor;
use candle_nn::{Conv1dConfig, Init, Mo<PERSON>le, VarBuilder, VarMap, ops::softmax};

#[derive(Clone)]
pub struct MatmulLayer {
    pub dataset_var: DatasetVar,
}

#[derive(Clone, Debug)]
pub struct DatasetVar {
    pub weight: Tensor,
}

impl DatasetVar {
    fn dataset_var(dataset: Tensor, vb: &VarBuilder, name: &str) -> anyhow::Result<DatasetVar> {
        let weight = vb.get_with_hints(
            dataset.dims(),
            format!("dataset_weight_{name}").as_str(),
            candle_nn::Init::Randn {
                mean: 0.5,
                stdev: 0.25,
            },
        )?;

        Ok(DatasetVar { weight })
    }
}

impl MatmulLayer {
    pub fn load(vb: &VarBuilder, dataset: Tensor, name: &str) -> anyhow::Result<Self> {
        let dataset_var = DatasetVar::dataset_var(dataset, &vb, name)?;

        Ok(Self { dataset_var })
    }

    pub fn init(&self, dataset: Tensor, mut vm: VarMap) -> anyhow::Result<VarMap> {
        vm.set_one("dataset_weight".to_string(), dataset)?;
        Ok(vm)
    }

    /// returns: pred_t, pred_t_conv, norm_probs
    /// input_tensor: [batch_size, 784]
    pub fn forward(&self, input_tensor: &Tensor, temp: f64) -> candle_core::Result<(Tensor, Tensor)> {
        let probs = self.dataset_var.weight.matmul(&input_tensor.t()?)?;
        let norm_probs = tempered_softmax(&probs,temp, 0)?;
        let pred_t = norm_probs.t()?.matmul(&self.dataset_var.weight)?;

        /*let pred_probs = tempered_softmax(
            &self.dataset_var.weight.matmul(
                &input_tensor.t()?)?,
                1.0,
            0,
        )?;*/

        Ok((pred_t, norm_probs))
    }
}

pub fn tempered_softmax(
  xs: &Tensor,
  temp: f64,
  dim: usize,
) -> candle_core::Result<Tensor> {
  let scaled_logits = (xs / temp)?;
  softmax(&scaled_logits, dim)
}